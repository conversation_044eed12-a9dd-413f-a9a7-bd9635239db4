package com.signaturedetection;

import org.opencv.core.*;
import org.opencv.imgproc.Imgproc;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Utility class for removing horizontal and vertical lines from scanned documents.
 * Uses morphological operations to detect and remove lines while preserving text and signatures.
 */
public class LineRemover {
    private static final Logger logger = LoggerFactory.getLogger(LineRemover.class);
    
    /**
     * Removes both horizontal and vertical lines from the input image.
     * 
     * @param inputImage The input binary image (Mat)
     * @return Mat with lines removed
     */
    public static Mat removeLines(Mat inputImage) {
        logger.info("Starting line removal process");
        
        // Create a copy of the input image
        Mat result = inputImage.clone();
        
        // Remove horizontal lines
        Mat withoutHorizontal = removeHorizontalLines(result);
        
        // Remove vertical lines from the result
        Mat withoutVertical = removeVerticalLines(withoutHorizontal);
        
        logger.info("Line removal process completed");
        return withoutVertical;
    }
    
    /**
     * Removes horizontal lines from the input image using morphological operations.
     * 
     * @param inputImage The input binary image
     * @return Mat with horizontal lines removed
     */
    public static Mat removeHorizontalLines(Mat inputImage) {
        logger.debug("Removing horizontal lines");
        
        // Create horizontal kernel
        int horizontalSize = inputImage.cols() / 30; // Adjust based on image size
        Mat horizontalKernel = Imgproc.getStructuringElement(
            Imgproc.MORPH_RECT, 
            new Size(horizontalSize, 1)
        );
        
        // Detect horizontal lines
        Mat horizontalLines = new Mat();
        Imgproc.erode(inputImage, horizontalLines, horizontalKernel);
        Imgproc.dilate(horizontalLines, horizontalLines, horizontalKernel);
        
        // Remove horizontal lines from original image
        Mat result = new Mat();
        Core.subtract(inputImage, horizontalLines, result);
        
        horizontalKernel.release();
        horizontalLines.release();
        
        return result;
    }
    
    /**
     * Removes vertical lines from the input image using morphological operations.
     * 
     * @param inputImage The input binary image
     * @return Mat with vertical lines removed
     */
    public static Mat removeVerticalLines(Mat inputImage) {
        logger.debug("Removing vertical lines");
        
        // Create vertical kernel
        int verticalSize = inputImage.rows() / 30; // Adjust based on image size
        Mat verticalKernel = Imgproc.getStructuringElement(
            Imgproc.MORPH_RECT, 
            new Size(1, verticalSize)
        );
        
        // Detect vertical lines
        Mat verticalLines = new Mat();
        Imgproc.erode(inputImage, verticalLines, verticalKernel);
        Imgproc.dilate(verticalLines, verticalLines, verticalKernel);
        
        // Remove vertical lines from original image
        Mat result = new Mat();
        Core.subtract(inputImage, verticalLines, result);
        
        verticalKernel.release();
        verticalLines.release();
        
        return result;
    }
    
    /**
     * Removes lines with custom kernel sizes for fine-tuning.
     * 
     * @param inputImage The input binary image
     * @param horizontalKernelSize Size of horizontal kernel
     * @param verticalKernelSize Size of vertical kernel
     * @return Mat with lines removed
     */
    public static Mat removeLinesCustom(Mat inputImage, int horizontalKernelSize, int verticalKernelSize) {
        logger.info("Removing lines with custom kernel sizes: horizontal={}, vertical={}", 
                   horizontalKernelSize, verticalKernelSize);
        
        Mat result = inputImage.clone();
        
        // Remove horizontal lines with custom size
        if (horizontalKernelSize > 0) {
            Mat horizontalKernel = Imgproc.getStructuringElement(
                Imgproc.MORPH_RECT, 
                new Size(horizontalKernelSize, 1)
            );
            
            Mat horizontalLines = new Mat();
            Imgproc.erode(result, horizontalLines, horizontalKernel);
            Imgproc.dilate(horizontalLines, horizontalLines, horizontalKernel);
            Core.subtract(result, horizontalLines, result);
            
            horizontalKernel.release();
            horizontalLines.release();
        }
        
        // Remove vertical lines with custom size
        if (verticalKernelSize > 0) {
            Mat verticalKernel = Imgproc.getStructuringElement(
                Imgproc.MORPH_RECT, 
                new Size(1, verticalKernelSize)
            );
            
            Mat verticalLines = new Mat();
            Imgproc.erode(result, verticalLines, verticalKernel);
            Imgproc.dilate(verticalLines, verticalLines, verticalKernel);
            Core.subtract(result, verticalLines, result);
            
            verticalKernel.release();
            verticalLines.release();
        }
        
        return result;
    }
}
