# Signature Detection and Line Removal

A Java application for detecting and extracting signatures from TIFF images while removing unwanted lines from scanned documents.

## Features

- **TIFF Image Support**: Load and process TIFF images using both OpenCV and Java ImageIO
- **Advanced Noise Removal**: Comprehensive noise reduction including salt-and-pepper, speckle, and scanning artifacts
- **Line Removal**: Remove horizontal and vertical lines from scanned documents using morphological operations
- **Signature & Initials Detection**: Intelligently distinguish between full signatures and initials based on size, aspect ratio, and complexity
- **Comprehensive Extraction**: Extract both signatures and initials as separate images with confidence scoring
- **Multiple Detection Modes**: Default, sensitive, strict, and custom configurations for different document types
- **Customizable Parameters**: Fine-tune detection and noise removal parameters for specific requirements
- **Advanced Processing Options**: Light, standard, aggressive, and advanced noise removal modes
- **Rich Output**: Generate processed images, extracted signatures/initials, visualizations, and detailed reports

## Requirements

- Java 11 or higher
- Maven 3.6 or higher

## Dependencies

- OpenCV for Java (4.9.0)
- TwelveMonkeys ImageIO for enhanced TIFF support
- Apache Commons IO
- SLF4J with Logback for logging
- JUnit 5 for testing

## Installation

1. <PERSON>lone or download the project
2. Build the project using Maven:

```bash
mvn clean compile
```

## Usage

### Basic Usage

Extract all signatures and initials from a TIFF image:

```bash
mvn exec:java -Dexec.mainClass="com.signaturedetection.SignatureAndInitialsExtractor" -Dexec.args="path/to/your/document.tiff ./output/"
```

Or use the original signature detection:

```bash
mvn exec:java -Dexec.args="path/to/your/document.tiff ./output/"
```

### Programmatic Usage

```java
import com.signaturedetection.*;

// Extract both signatures and initials (recommended approach)
SignatureAndInitialsExtractor.ExtractionResult result =
    SignatureAndInitialsExtractor.extractSignaturesAndInitials("document.tiff", "./output/");

// Different detection modes
SignatureAndInitialsExtractor.ExtractionResult sensitiveResult =
    SignatureAndInitialsExtractor.extractWithSensitiveDetection("document.tiff", "./output/");

SignatureAndInitialsExtractor.ExtractionResult strictResult =
    SignatureAndInitialsExtractor.extractWithStrictDetection("document.tiff", "./output/");

// Custom configuration
SignatureAndInitialsDetector.DetectionConfig customConfig = new SignatureAndInitialsDetector.DetectionConfig();
customConfig.minSignatureArea = 800;
customConfig.minInitialsArea = 150;
customConfig.confidenceThreshold = 0.6;
SignatureAndInitialsExtractor.ExtractionResult customResult =
    SignatureAndInitialsExtractor.extractSignaturesAndInitials("document.tiff", "./output/", customConfig);

// Check results
if (result.isSuccess()) {
    System.out.println("Found " + result.getSignatureCount() + " signatures and " +
                      result.getInitialsCount() + " initials");

    // Access signatures
    for (SignatureAndInitialsDetector.DetectedItem signature : result.getSignatures()) {
        System.out.println("Signature at " + signature.getBoundingRect() +
                          " confidence: " + signature.getConfidence());
    }

    // Access initials
    for (SignatureAndInitialsDetector.DetectedItem initials : result.getInitials()) {
        System.out.println("Initials at " + initials.getBoundingRect() +
                          " confidence: " + initials.getConfidence());
    }

    // Get extracted images
    for (SignatureAndInitialsDetector.ExtractedItem item : result.getExtractedItems()) {
        System.out.println("Extracted " + item.getType() + " " + item.getId());
        // item.getImage() contains the actual image data
    }

    System.out.println("Processing time: " + result.getProcessingTimeMs() + "ms");
} else {
    System.out.println("Extraction failed: " + result.getErrorMessage());
}

// Legacy processing (saves files but doesn't return structured results)
SignatureDetectionApp.processDocument("document.tiff", "./output/");

// Custom processing with fine-tuned parameters
SignatureDetectionApp.processDocumentCustom(
    "document.tiff",           // Input path
    "./output/",               // Output directory
    50,                        // Horizontal kernel size for line removal
    30,                        // Vertical kernel size for line removal
    1000,                      // Minimum signature area
    50000,                     // Maximum signature area
    1.5,                       // Minimum aspect ratio
    8.0                        // Maximum aspect ratio
);
```

### Individual Components

```java
import com.signaturedetection.*;
import org.opencv.core.Mat;
import org.opencv.core.Rect;
import java.util.List;

// Load and preprocess image with noise removal
Mat originalImage = ImageProcessor.loadTiffImage("document.tiff");
Mat preprocessedImage = ImageProcessor.preprocessImage(originalImage); // Includes noise removal

// Advanced preprocessing for heavily degraded images
Mat advancedPreprocessed = ImageProcessor.preprocessImageAdvanced(originalImage);

// Custom noise removal
NoiseRemover.NoiseRemovalConfig config = NoiseRemover.NoiseRemovalConfig.createAggressiveConfig();
Mat customPreprocessed = ImageProcessor.preprocessImage(originalImage, config);

// Individual noise removal techniques
Mat denoisedImage = NoiseRemover.removeNoise(originalImage);
Mat saltPepperCleaned = NoiseRemover.removeSaltPepperNoise(originalImage, 5);
Mat speckleCleaned = NoiseRemover.removeSpeckleNoise(originalImage, 3);
Mat artifactsCleaned = NoiseRemover.removeScanningArtifacts(originalImage);

// Remove lines
Mat imageWithoutLines = LineRemover.removeLines(preprocessedImage);

// Detect signatures
List<Rect> signatureRegions = SignatureDetector.detectSignatureRegions(imageWithoutLines);

// Extract signatures
List<Mat> signatures = SignatureDetector.extractSignatures(originalImage, signatureRegions);

// Save results
ImageProcessor.saveImage(imageWithoutLines, "processed.png");
for (int i = 0; i < signatures.size(); i++) {
    ImageProcessor.saveImage(signatures.get(i), "signature_" + (i + 1) + ".png");
}
```

## Output Files

The signature and initials extractor generates several output files:

- `01_preprocessed.png`: Preprocessed image with noise removal
- `02_lines_removed.png`: Image with horizontal and vertical lines removed
- `03_detection_result.png`: Visualization showing detected signatures (green) and initials (red) with confidence scores
- `signature_1.png`, `signature_2.png`, etc.: Individual extracted signature images
- `initials_1.png`, `initials_2.png`, etc.: Individual extracted initials images
- `extraction_report.txt`: Detailed report with detection statistics and item details

## Signature vs Initials Detection

The system intelligently distinguishes between signatures and initials based on:

### Signatures
- **Size**: Typically larger areas (1000-50000 pixels)
- **Aspect Ratio**: Usually wider than tall (2.0-8.0 ratio)
- **Complexity**: More complex shapes with multiple strokes
- **Dimensions**: Minimum 80x25 pixels

### Initials
- **Size**: Typically smaller areas (200-3000 pixels)
- **Aspect Ratio**: More square or compact (0.3-3.0 ratio)
- **Complexity**: Simpler, more geometric shapes
- **Dimensions**: Minimum 15x15 pixels

### Detection Modes
- **Default**: Balanced detection for most documents
- **Sensitive**: Lower thresholds, finds more items (may include false positives)
- **Strict**: Higher thresholds, higher confidence (may miss some items)
- **Custom**: User-defined parameters for specific document types

## Result Object for Testing

The `SignatureDetectionResult` object provides structured access to detection results:

```java
// Access detected signatures with coordinates
for (SignatureDetectionResult.DetectedSignature signature : result.getSignatures()) {
    // Get coordinates
    int x = signature.getX();
    int y = signature.getY();
    int width = signature.getWidth();
    int height = signature.getHeight();

    // Get calculated properties
    double area = signature.getArea();
    double aspectRatio = signature.getAspectRatio();

    // Get coordinate points
    Point topLeft = signature.getTopLeft();
    Point center = signature.getCenter();
    Point bottomRight = signature.getBottomRight();
}

// Filter signatures by properties
List<DetectedSignature> largeSignatures = result.getSignaturesLargerThan(5000);
List<DetectedSignature> wideSignatures = result.getSignaturesByAspectRatio(3.0, 8.0);
List<DetectedSignature> topHalfSignatures = result.getSignaturesInArea(0, 0, imageWidth, imageHeight/2);

// Test against expected coordinates
DetectedSignature signature = result.getSignatureById(1);
assertEquals(150, signature.getX(), 20); // ±20 pixel tolerance
assertEquals(200, signature.getY(), 20);

// Get processing statistics
ProcessingStats stats = result.getStats();
long processingTime = stats.getProcessingTimeMs();
int imageWidth = stats.getImageWidth();
int imageHeight = stats.getImageHeight();
```

## Algorithm Details

### Noise Removal

1. **Gaussian Denoising**: Reduces general noise while preserving edges
2. **Median Filtering**: Effectively removes salt-and-pepper noise
3. **Morphological Operations**: Removes small artifacts and speckle noise
4. **Connected Component Filtering**: Removes isolated noise components based on size
5. **Bilateral Filtering**: Edge-preserving smoothing for advanced denoising
6. **Non-Local Means**: Advanced texture-preserving denoising
7. **Scanning Artifact Removal**: Specialized removal of scanner-specific noise

### Line Removal

1. **Horizontal Line Detection**: Uses morphological operations with horizontal kernels to detect and remove horizontal lines
2. **Vertical Line Detection**: Uses morphological operations with vertical kernels to detect and remove vertical lines
3. **Preservation**: Carefully designed to preserve text and signature content while removing unwanted lines

### Signature Detection

1. **Preprocessing**: Applies Gaussian blur, adaptive thresholding, and morphological operations
2. **Contour Analysis**: Finds contours in the processed image
3. **Feature Filtering**: Filters contours based on:
   - Area (1000-50000 pixels by default)
   - Aspect ratio (1.5-8.0 by default)
   - Complexity (minimum 50 contour points)
   - Minimum dimensions (50x20 pixels)

### Customization Parameters

#### Noise Removal Parameters
- **Gaussian Kernel Size**: Controls the amount of Gaussian blur (3-7 typical range)
- **Median Kernel Size**: Size of median filter for salt-and-pepper noise (3-9 typical range)
- **Morphological Kernel Size**: Size of morphological operations for artifact removal (1-5 typical range)
- **Component Area Thresholds**: Min/max area for connected component filtering
- **Bilateral Filter Parameters**: Controls edge-preserving smoothing strength
- **Non-Local Means Parameters**: Advanced denoising strength and window sizes

#### Line Removal Parameters
- **Kernel Sizes**: Adjust horizontal and vertical kernel sizes for line removal based on line thickness
- **Area Constraints**: Set minimum and maximum signature areas based on expected signature sizes
- **Aspect Ratio**: Configure aspect ratio range based on signature characteristics
- **Morphological Operations**: Fine-tune preprocessing parameters for different image qualities

#### Noise Removal Configurations
- **Light Config**: Minimal noise removal for high-quality scans
- **Standard Config**: Balanced approach for typical office documents
- **Aggressive Config**: Strong noise removal for degraded documents
- **Custom Config**: User-defined parameters for specific noise types

## Testing

Run the test suite:

```bash
mvn test
```

The tests cover:
- Image preprocessing functionality
- Line removal algorithms
- Signature detection and extraction
- Custom parameter handling
- File I/O operations

## Troubleshooting

### Common Issues

1. **OpenCV Loading Issues**: Ensure the OpenCV native library is properly loaded
2. **TIFF Format Issues**: The application uses TwelveMonkeys ImageIO as a fallback for complex TIFF formats
3. **Memory Issues**: Large images may require increased JVM heap size: `-Xmx2g`
4. **No Signatures Detected**: Try adjusting detection parameters for your specific document type

### Performance Tips

- Use appropriate image resolution (300 DPI is typically sufficient)
- Consider preprocessing images to remove noise before processing
- Adjust kernel sizes based on the thickness of lines in your documents
- Fine-tune signature detection parameters based on your signature characteristics

## License

This project is provided as-is for educational and development purposes.
