package com.signaturedetection;

import org.opencv.core.Mat;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.List;

/**
 * Main application for extracting both signatures and initials from TIFF files.
 * Provides comprehensive detection and extraction of handwritten signatures and initials.
 */
public class SignatureAndInitialsExtractor {
    private static final Logger logger = LoggerFactory.getLogger(SignatureAndInitialsExtractor.class);
    
    /**
     * Result object containing both signatures and initials extraction results.
     */
    public static class ExtractionResult {
        private final List<SignatureAndInitialsDetector.DetectedItem> allDetectedItems;
        private final List<SignatureAndInitialsDetector.DetectedItem> signatures;
        private final List<SignatureAndInitialsDetector.DetectedItem> initials;
        private final List<SignatureAndInitialsDetector.ExtractedItem> extractedItems;
        private final long processingTimeMs;
        private final String inputPath;
        private final boolean success;
        private final String errorMessage;
        
        public ExtractionResult(List<SignatureAndInitialsDetector.DetectedItem> allDetectedItems,
                               List<SignatureAndInitialsDetector.ExtractedItem> extractedItems,
                               long processingTimeMs, String inputPath) {
            this.allDetectedItems = allDetectedItems;
            this.signatures = SignatureAndInitialsDetector.getSignatures(allDetectedItems);
            this.initials = SignatureAndInitialsDetector.getInitials(allDetectedItems);
            this.extractedItems = extractedItems;
            this.processingTimeMs = processingTimeMs;
            this.inputPath = inputPath;
            this.success = true;
            this.errorMessage = null;
        }
        
        public ExtractionResult(String errorMessage) {
            this.allDetectedItems = null;
            this.signatures = null;
            this.initials = null;
            this.extractedItems = null;
            this.processingTimeMs = 0;
            this.inputPath = null;
            this.success = false;
            this.errorMessage = errorMessage;
        }
        
        // Getters
        public List<SignatureAndInitialsDetector.DetectedItem> getAllDetectedItems() { return allDetectedItems; }
        public List<SignatureAndInitialsDetector.DetectedItem> getSignatures() { return signatures; }
        public List<SignatureAndInitialsDetector.DetectedItem> getInitials() { return initials; }
        public List<SignatureAndInitialsDetector.ExtractedItem> getExtractedItems() { return extractedItems; }
        public long getProcessingTimeMs() { return processingTimeMs; }
        public String getInputPath() { return inputPath; }
        public boolean isSuccess() { return success; }
        public String getErrorMessage() { return errorMessage; }
        
        public int getSignatureCount() { return signatures != null ? signatures.size() : 0; }
        public int getInitialsCount() { return initials != null ? initials.size() : 0; }
        public int getTotalCount() { return getSignatureCount() + getInitialsCount(); }
        
        /**
         * Generate a summary report string.
         */
        public String generateSummary() {
            if (!success) {
                return "Extraction failed: " + errorMessage;
            }
            
            StringBuilder sb = new StringBuilder();
            sb.append("Signature and Initials Extraction Summary\n");
            sb.append("========================================\n");
            sb.append("Input: ").append(inputPath).append("\n");
            sb.append("Processing time: ").append(processingTimeMs).append("ms\n");
            sb.append("Total items found: ").append(getTotalCount()).append("\n");
            sb.append("Signatures: ").append(getSignatureCount()).append("\n");
            sb.append("Initials: ").append(getInitialsCount()).append("\n\n");
            
            if (getSignatureCount() > 0) {
                sb.append("Detected Signatures:\n");
                for (int i = 0; i < signatures.size(); i++) {
                    SignatureAndInitialsDetector.DetectedItem sig = signatures.get(i);
                    sb.append(String.format("  Signature %d: area=%.0f, ratio=%.2f, confidence=%.2f, bounds=%s\n",
                            i + 1, sig.getArea(), sig.getAspectRatio(), sig.getConfidence(), sig.getBoundingRect()));
                }
                sb.append("\n");
            }
            
            if (getInitialsCount() > 0) {
                sb.append("Detected Initials:\n");
                for (int i = 0; i < initials.size(); i++) {
                    SignatureAndInitialsDetector.DetectedItem init = initials.get(i);
                    sb.append(String.format("  Initials %d: area=%.0f, ratio=%.2f, confidence=%.2f, bounds=%s\n",
                            i + 1, init.getArea(), init.getAspectRatio(), init.getConfidence(), init.getBoundingRect()));
                }
            }
            
            return sb.toString();
        }
    }
    
    public static void main(String[] args) {
        if (args.length < 1) {
            System.out.println("Usage: java SignatureAndInitialsExtractor <input-tiff-path> [output-directory]");
            System.out.println("Example: java SignatureAndInitialsExtractor document.tiff ./output/");
            return;
        }
        
        String inputPath = args[0];
        String outputDir = args.length > 1 ? args[1] : "./output/";
        
        try {
            ExtractionResult result = extractSignaturesAndInitials(inputPath, outputDir);
            
            // Print results
            System.out.println("=== SIGNATURE AND INITIALS EXTRACTION RESULTS ===");
            System.out.println(result.generateSummary());
            
            if (result.isSuccess()) {
                System.out.println("Extraction completed successfully!");
                System.out.println("Check the output directory for extracted images: " + outputDir);
            } else {
                System.err.println("Extraction failed: " + result.getErrorMessage());
            }
            
        } catch (Exception e) {
            logger.error("Error in signature and initials extraction: {}", e.getMessage(), e);
            System.err.println("Error: " + e.getMessage());
        }
    }
    
    /**
     * Extracts all signatures and initials from a TIFF file.
     * 
     * @param inputPath Path to the input TIFF image
     * @param outputDir Directory to save extracted images
     * @return ExtractionResult containing all detected and extracted items
     */
    public static ExtractionResult extractSignaturesAndInitials(String inputPath, String outputDir) throws IOException {
        return extractSignaturesAndInitials(inputPath, outputDir, SignatureAndInitialsDetector.DetectionConfig.createDefault());
    }
    
    /**
     * Extracts signatures and initials with custom detection configuration.
     * 
     * @param inputPath Path to the input TIFF image
     * @param outputDir Directory to save extracted images
     * @param config Detection configuration
     * @return ExtractionResult containing all detected and extracted items
     */
    public static ExtractionResult extractSignaturesAndInitials(String inputPath, String outputDir, 
                                                               SignatureAndInitialsDetector.DetectionConfig config) throws IOException {
        long startTime = System.currentTimeMillis();
        
        try {
            logger.info("Starting signature and initials extraction: {}", inputPath);
            
            // Create output directory
            java.io.File outputDirectory = new java.io.File(outputDir);
            if (!outputDirectory.exists()) {
                outputDirectory.mkdirs();
            } else {
                // Clear the directory
                for (java.io.File file : outputDirectory.listFiles()) {
                    if (file.isFile()) {
                        file.delete();
                    }
                }
            }
            
            // Step 1: Load the TIFF image
            Mat originalImage = ImageProcessor.loadTiffImage(inputPath);
            
            // Step 2: Preprocess the image with noise removal
            Mat preprocessedImage = ImageProcessor.preprocessImage(originalImage);
            
            // Save preprocessed image for inspection
            String preprocessedPath = outputDir + "/01_preprocessed.png";
            ImageProcessor.saveImage(preprocessedImage, preprocessedPath);
            logger.info("Saved preprocessed image: {}", preprocessedPath);
            
            // Step 3: Remove lines from the image
            Mat imageWithoutLines = LineRemover.removeLines(preprocessedImage);
            
            // Save image without lines
            String noLinesPath = outputDir + "/02_lines_removed.png";
            ImageProcessor.saveImage(imageWithoutLines, noLinesPath);
            logger.info("Saved image without lines: {}", noLinesPath);
            
            // Step 4: Detect signatures and initials
            List<SignatureAndInitialsDetector.DetectedItem> detectedItems = 
                SignatureAndInitialsDetector.detectSignaturesAndInitials(imageWithoutLines, config);
            
            // Step 5: Extract the detected items
            List<SignatureAndInitialsDetector.ExtractedItem> extractedItems = 
                SignatureAndInitialsDetector.extractItems(originalImage, detectedItems);
            
            // Step 6: Save extracted items
            saveExtractedItems(extractedItems, outputDir);
            
            // Step 7: Create visualization
            Mat visualization = createVisualization(originalImage, detectedItems);
            String visualizationPath = outputDir + "/03_detection_result.png";
            ImageProcessor.saveImage(visualization, visualizationPath);
            logger.info("Saved visualization: {}", visualizationPath);
            
            // Step 8: Generate detailed report
            generateDetailedReport(detectedItems, extractedItems, outputDir, inputPath);
            
            // Clean up
            originalImage.release();
            preprocessedImage.release();
            imageWithoutLines.release();
            visualization.release();
            for (SignatureAndInitialsDetector.ExtractedItem item : extractedItems) {
                item.release();
            }
            
            long processingTime = System.currentTimeMillis() - startTime;
            logger.info("Signature and initials extraction completed successfully in {}ms", processingTime);
            
            return new ExtractionResult(detectedItems, extractedItems, processingTime, inputPath);
            
        } catch (Exception e) {
            logger.error("Error during extraction: {}", e.getMessage(), e);
            return new ExtractionResult("Extraction failed: " + e.getMessage());
        }
    }
    
    /**
     * Extracts with sensitive detection (finds more items, may include false positives).
     */
    public static ExtractionResult extractWithSensitiveDetection(String inputPath, String outputDir) throws IOException {
        return extractSignaturesAndInitials(inputPath, outputDir, SignatureAndInitialsDetector.DetectionConfig.createSensitive());
    }
    
    /**
     * Extracts with strict detection (finds fewer items, higher confidence).
     */
    public static ExtractionResult extractWithStrictDetection(String inputPath, String outputDir) throws IOException {
        return extractSignaturesAndInitials(inputPath, outputDir, SignatureAndInitialsDetector.DetectionConfig.createStrict());
    }
    
    /**
     * Saves all extracted items to individual files.
     */
    private static void saveExtractedItems(List<SignatureAndInitialsDetector.ExtractedItem> extractedItems, 
                                         String outputDir) throws IOException {
        
        int signatureCount = 1;
        int initialsCount = 1;
        
        for (SignatureAndInitialsDetector.ExtractedItem item : extractedItems) {
            String filename;
            if (item.getType() == SignatureAndInitialsDetector.DetectedItem.Type.SIGNATURE) {
                filename = "signature_" + signatureCount + ".png";
                signatureCount++;
            } else {
                filename = "initials_" + initialsCount + ".png";
                initialsCount++;
            }
            
            String filePath = outputDir + "/" + filename;
            ImageProcessor.saveImage(item.getImage(), filePath);
            logger.info("Saved {}: {}", item.getType().toString().toLowerCase(), filePath);
        }
    }
    
    /**
     * Creates a visualization showing all detected signatures and initials.
     */
    private static Mat createVisualization(Mat originalImage, List<SignatureAndInitialsDetector.DetectedItem> detectedItems) {
        Mat visualization = new Mat();
        
        // Convert to color if grayscale
        if (originalImage.channels() == 1) {
            org.opencv.imgproc.Imgproc.cvtColor(originalImage, visualization, org.opencv.imgproc.Imgproc.COLOR_GRAY2BGR);
        } else {
            visualization = originalImage.clone();
        }
        
        // Draw rectangles around detected items
        for (SignatureAndInitialsDetector.DetectedItem item : detectedItems) {
            org.opencv.core.Scalar color;
            if (item.getType() == SignatureAndInitialsDetector.DetectedItem.Type.SIGNATURE) {
                color = new org.opencv.core.Scalar(0, 255, 0); // Green for signatures
            } else {
                color = new org.opencv.core.Scalar(0, 0, 255); // Red for initials
            }
            
            org.opencv.imgproc.Imgproc.rectangle(visualization, item.getBoundingRect().tl(), 
                                               item.getBoundingRect().br(), color, 2);
            
            // Add label
            String label = item.getType().toString() + String.format(" (%.2f)", item.getConfidence());
            org.opencv.imgproc.Imgproc.putText(visualization, label, 
                                             new org.opencv.core.Point(item.getBoundingRect().x, item.getBoundingRect().y - 5),
                                             org.opencv.imgproc.Imgproc.FONT_HERSHEY_SIMPLEX, 0.5, color, 1);
        }
        
        return visualization;
    }
    
    /**
     * Generates a detailed report of the extraction results.
     */
    private static void generateDetailedReport(List<SignatureAndInitialsDetector.DetectedItem> detectedItems,
                                             List<SignatureAndInitialsDetector.ExtractedItem> extractedItems,
                                             String outputDir, String inputPath) throws IOException {
        
        String reportPath = outputDir + "/extraction_report.txt";
        
        try (java.io.PrintWriter writer = new java.io.PrintWriter(reportPath)) {
            writer.println("Signature and Initials Extraction Report");
            writer.println("=======================================");
            writer.println("Generated: " + java.time.LocalDateTime.now());
            writer.println("Input file: " + inputPath);
            writer.println();
            
            List<SignatureAndInitialsDetector.DetectedItem> signatures = SignatureAndInitialsDetector.getSignatures(detectedItems);
            List<SignatureAndInitialsDetector.DetectedItem> initials = SignatureAndInitialsDetector.getInitials(detectedItems);
            
            writer.println("Summary:");
            writer.println("--------");
            writer.println("Total items detected: " + detectedItems.size());
            writer.println("Signatures: " + signatures.size());
            writer.println("Initials: " + initials.size());
            writer.println();
            
            if (!signatures.isEmpty()) {
                writer.println("Detected Signatures:");
                writer.println("-------------------");
                for (int i = 0; i < signatures.size(); i++) {
                    SignatureAndInitialsDetector.DetectedItem sig = signatures.get(i);
                    writer.println("Signature " + (i + 1) + ":");
                    writer.println("  Position: (" + sig.getBoundingRect().x + ", " + sig.getBoundingRect().y + ")");
                    writer.println("  Size: " + sig.getBoundingRect().width + "x" + sig.getBoundingRect().height);
                    writer.println("  Area: " + (int)sig.getArea() + " pixels");
                    writer.println("  Aspect Ratio: " + String.format("%.2f", sig.getAspectRatio()));
                    writer.println("  Confidence: " + String.format("%.2f", sig.getConfidence()));
                    writer.println("  Complexity: " + String.format("%.2f", sig.getComplexity()));
                    writer.println("  Saved as: signature_" + (i + 1) + ".png");
                    writer.println();
                }
            }
            
            if (!initials.isEmpty()) {
                writer.println("Detected Initials:");
                writer.println("-----------------");
                for (int i = 0; i < initials.size(); i++) {
                    SignatureAndInitialsDetector.DetectedItem init = initials.get(i);
                    writer.println("Initials " + (i + 1) + ":");
                    writer.println("  Position: (" + init.getBoundingRect().x + ", " + init.getBoundingRect().y + ")");
                    writer.println("  Size: " + init.getBoundingRect().width + "x" + init.getBoundingRect().height);
                    writer.println("  Area: " + (int)init.getArea() + " pixels");
                    writer.println("  Aspect Ratio: " + String.format("%.2f", init.getAspectRatio()));
                    writer.println("  Confidence: " + String.format("%.2f", init.getConfidence()));
                    writer.println("  Complexity: " + String.format("%.2f", init.getComplexity()));
                    writer.println("  Saved as: initials_" + (i + 1) + ".png");
                    writer.println();
                }
            }
            
            writer.println("Output Files:");
            writer.println("------------");
            writer.println("01_preprocessed.png - Preprocessed image");
            writer.println("02_lines_removed.png - Image with lines removed");
            writer.println("03_detection_result.png - Visualization with detected items");
            writer.println("signature_*.png - Individual signature images");
            writer.println("initials_*.png - Individual initials images");
            writer.println("extraction_report.txt - This report");
        }
        
        logger.info("Saved detailed report: {}", reportPath);
    }
}
