package com.signaturedetection;

import org.opencv.core.*;
import org.opencv.imgproc.Imgproc;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;

/**
 * Enhanced detector for both signatures and initials in scanned documents.
 * Distinguishes between full signatures and initials based on size, aspect ratio, and complexity.
 */
public class SignatureAndInitialsDetector {
    private static final Logger logger = LoggerFactory.getLogger(SignatureAndInitialsDetector.class);
    
    /**
     * Represents a detected signature or initial with classification.
     */
    public static class DetectedItem {
        public enum Type {
            SIGNATURE,
            INITIALS,
            UNKNOWN
        }
        
        private final Rect boundingRect;
        private final double area;
        private final double aspectRatio;
        private final Type type;
        private final double confidence;
        private final int contourPoints;
        private final double complexity;
        
        public DetectedItem(Rect boundingRect, double area, double aspectRatio, 
                           Type type, double confidence, int contourPoints, double complexity) {
            this.boundingRect = boundingRect;
            this.area = area;
            this.aspectRatio = aspectRatio;
            this.type = type;
            this.confidence = confidence;
            this.contourPoints = contourPoints;
            this.complexity = complexity;
        }
        
        // Getters
        public Rect getBoundingRect() { return boundingRect; }
        public double getArea() { return area; }
        public double getAspectRatio() { return aspectRatio; }
        public Type getType() { return type; }
        public double getConfidence() { return confidence; }
        public int getContourPoints() { return contourPoints; }
        public double getComplexity() { return complexity; }
        
        public boolean isSignature() { return type == Type.SIGNATURE; }
        public boolean isInitials() { return type == Type.INITIALS; }
        
        @Override
        public String toString() {
            return String.format("%s[area=%.0f, ratio=%.2f, confidence=%.2f, bounds=%s]",
                    type, area, aspectRatio, confidence, boundingRect);
        }
    }
    
    /**
     * Configuration for signature and initials detection.
     */
    public static class DetectionConfig {
        // Signature parameters
        public int minSignatureArea = 1000;
        public int maxSignatureArea = 50000;
        public double minSignatureAspectRatio = 2.0;
        public double maxSignatureAspectRatio = 8.0;
        public int minSignatureWidth = 80;
        public int minSignatureHeight = 25;
        
        // Initials parameters
        public int minInitialsArea = 200;
        public int maxInitialsArea = 3000;
        public double minInitialsAspectRatio = 0.3;
        public double maxInitialsAspectRatio = 3.0;
        public int minInitialsWidth = 15;
        public int minInitialsHeight = 15;
        
        // General parameters
        public int minContourPoints = 10;
        public double minComplexity = 0.1;
        public double confidenceThreshold = 0.5;
        
        public static DetectionConfig createDefault() {
            return new DetectionConfig();
        }
        
        public static DetectionConfig createSensitive() {
            DetectionConfig config = new DetectionConfig();
            config.minSignatureArea = 500;
            config.minInitialsArea = 100;
            config.minContourPoints = 5;
            config.confidenceThreshold = 0.3;
            return config;
        }
        
        public static DetectionConfig createStrict() {
            DetectionConfig config = new DetectionConfig();
            config.minSignatureArea = 1500;
            config.minInitialsArea = 300;
            config.minContourPoints = 15;
            config.confidenceThreshold = 0.7;
            return config;
        }
    }
    
    /**
     * Detects both signatures and initials in the input image.
     * 
     * @param inputImage The preprocessed binary image
     * @return List of detected items (signatures and initials)
     */
    public static List<DetectedItem> detectSignaturesAndInitials(Mat inputImage) {
        return detectSignaturesAndInitials(inputImage, DetectionConfig.createDefault());
    }
    
    /**
     * Detects both signatures and initials with custom configuration.
     * 
     * @param inputImage The preprocessed binary image
     * @param config Detection configuration
     * @return List of detected items (signatures and initials)
     */
    public static List<DetectedItem> detectSignaturesAndInitials(Mat inputImage, DetectionConfig config) {
        logger.info("Starting signature and initials detection");
        
        List<DetectedItem> detectedItems = new ArrayList<>();
        
        // Find contours
        List<MatOfPoint> contours = new ArrayList<>();
        Mat hierarchy = new Mat();
        Imgproc.findContours(inputImage, contours, hierarchy, 
                           Imgproc.RETR_EXTERNAL, Imgproc.CHAIN_APPROX_SIMPLE);
        
        logger.debug("Found {} contours for analysis", contours.size());
        
        // Analyze each contour
        for (MatOfPoint contour : contours) {
            DetectedItem item = analyzeContour(contour, config);
            if (item != null && item.getConfidence() >= config.confidenceThreshold) {
                detectedItems.add(item);
                logger.debug("Detected {}: {}", item.getType(), item);
            }
        }
        
        hierarchy.release();
        
        // Post-process to remove overlapping detections
        List<DetectedItem> filteredItems = removeOverlappingDetections(detectedItems);
        
        logger.info("Detected {} signatures and {} initials", 
                   filteredItems.stream().mapToInt(item -> item.isSignature() ? 1 : 0).sum(),
                   filteredItems.stream().mapToInt(item -> item.isInitials() ? 1 : 0).sum());
        
        return filteredItems;
    }
    
    /**
     * Extracts only signatures from the detected items.
     */
    public static List<DetectedItem> getSignatures(List<DetectedItem> detectedItems) {
        return detectedItems.stream()
                .filter(DetectedItem::isSignature)
                .collect(java.util.stream.Collectors.toList());
    }
    
    /**
     * Extracts only initials from the detected items.
     */
    public static List<DetectedItem> getInitials(List<DetectedItem> detectedItems) {
        return detectedItems.stream()
                .filter(DetectedItem::isInitials)
                .collect(java.util.stream.Collectors.toList());
    }
    
    /**
     * Extracts signature and initial images from the original image.
     * 
     * @param originalImage The original image
     * @param detectedItems List of detected items
     * @return List of extracted images with their types
     */
    public static List<ExtractedItem> extractItems(Mat originalImage, List<DetectedItem> detectedItems) {
        logger.info("Extracting {} items", detectedItems.size());
        
        List<ExtractedItem> extractedItems = new ArrayList<>();
        
        for (int i = 0; i < detectedItems.size(); i++) {
            DetectedItem item = detectedItems.get(i);
            Rect region = item.getBoundingRect();
            
            // Ensure the region is within image bounds
            Rect clampedRegion = clampRectToImage(region, originalImage);
            
            // Extract the region
            Mat extractedImage = new Mat(originalImage, clampedRegion);
            Mat extractedCopy = extractedImage.clone();
            
            ExtractedItem extractedItem = new ExtractedItem(extractedCopy, item, i + 1);
            extractedItems.add(extractedItem);
            
            logger.debug("Extracted {} {}: {}x{}", item.getType(), i + 1, 
                        clampedRegion.width, clampedRegion.height);
        }
        
        return extractedItems;
    }
    
    /**
     * Represents an extracted signature or initial image.
     */
    public static class ExtractedItem {
        private final Mat image;
        private final DetectedItem detectedItem;
        private final int id;
        
        public ExtractedItem(Mat image, DetectedItem detectedItem, int id) {
            this.image = image;
            this.detectedItem = detectedItem;
            this.id = id;
        }
        
        public Mat getImage() { return image; }
        public DetectedItem getDetectedItem() { return detectedItem; }
        public int getId() { return id; }
        public DetectedItem.Type getType() { return detectedItem.getType(); }
        
        public String getFileName() {
            String typeStr = detectedItem.getType().toString().toLowerCase();
            return typeStr + "_" + id + ".png";
        }
        
        public void release() {
            if (image != null) {
                image.release();
            }
        }
    }
    
    /**
     * Analyzes a contour to determine if it's a signature, initials, or neither.
     */
    private static DetectedItem analyzeContour(MatOfPoint contour, DetectionConfig config) {
        Rect boundingRect = Imgproc.boundingRect(contour);
        double area = Imgproc.contourArea(contour);
        double aspectRatio = (double) boundingRect.width / boundingRect.height;
        int contourPoints = (int) contour.total();
        
        // Calculate complexity (perimeter to area ratio)
        double perimeter = Imgproc.arcLength(new MatOfPoint2f(contour.toArray()), true);
        double complexity = perimeter * perimeter / (4 * Math.PI * area);
        
        // Check basic constraints
        if (contourPoints < config.minContourPoints || complexity < config.minComplexity) {
            return null;
        }
        
        // Classify as signature or initials
        DetectedItem.Type type = classifyItem(boundingRect, area, aspectRatio, complexity, config);
        if (type == DetectedItem.Type.UNKNOWN) {
            return null;
        }
        
        // Calculate confidence
        double confidence = calculateConfidence(boundingRect, area, aspectRatio, complexity, type, config);
        
        return new DetectedItem(boundingRect, area, aspectRatio, type, confidence, contourPoints, complexity);
    }
    
    /**
     * Classifies an item as signature, initials, or unknown.
     */
    private static DetectedItem.Type classifyItem(Rect boundingRect, double area, double aspectRatio, 
                                                 double complexity, DetectionConfig config) {
        
        // Check if it could be a signature
        if (area >= config.minSignatureArea && area <= config.maxSignatureArea &&
            aspectRatio >= config.minSignatureAspectRatio && aspectRatio <= config.maxSignatureAspectRatio &&
            boundingRect.width >= config.minSignatureWidth && boundingRect.height >= config.minSignatureHeight) {
            return DetectedItem.Type.SIGNATURE;
        }
        
        // Check if it could be initials
        if (area >= config.minInitialsArea && area <= config.maxInitialsArea &&
            aspectRatio >= config.minInitialsAspectRatio && aspectRatio <= config.maxInitialsAspectRatio &&
            boundingRect.width >= config.minInitialsWidth && boundingRect.height >= config.minInitialsHeight) {
            return DetectedItem.Type.INITIALS;
        }
        
        return DetectedItem.Type.UNKNOWN;
    }
    
    /**
     * Calculates confidence score for the classification.
     */
    private static double calculateConfidence(Rect boundingRect, double area, double aspectRatio, 
                                            double complexity, DetectedItem.Type type, DetectionConfig config) {
        double confidence = 0.0;
        
        if (type == DetectedItem.Type.SIGNATURE) {
            // Signature confidence factors
            double areaScore = Math.min(1.0, area / 5000.0); // Prefer larger areas
            double aspectScore = aspectRatio >= 3.0 && aspectRatio <= 6.0 ? 1.0 : 0.5; // Prefer typical signature ratios
            double complexityScore = Math.min(1.0, complexity / 2.0); // Prefer more complex shapes
            confidence = (areaScore + aspectScore + complexityScore) / 3.0;
            
        } else if (type == DetectedItem.Type.INITIALS) {
            // Initials confidence factors
            double areaScore = area >= 500 && area <= 1500 ? 1.0 : 0.7; // Prefer medium areas
            double aspectScore = aspectRatio >= 0.5 && aspectRatio <= 2.0 ? 1.0 : 0.6; // Prefer square-ish ratios
            double complexityScore = Math.min(1.0, complexity / 1.5); // Moderate complexity
            confidence = (areaScore + aspectScore + complexityScore) / 3.0;
        }
        
        return Math.max(0.0, Math.min(1.0, confidence));
    }
    
    /**
     * Removes overlapping detections, keeping the one with higher confidence.
     */
    private static List<DetectedItem> removeOverlappingDetections(List<DetectedItem> items) {
        List<DetectedItem> filtered = new ArrayList<>();
        
        for (DetectedItem item : items) {
            boolean overlaps = false;
            
            for (DetectedItem existing : filtered) {
                if (rectsOverlap(item.getBoundingRect(), existing.getBoundingRect())) {
                    if (item.getConfidence() > existing.getConfidence()) {
                        filtered.remove(existing);
                        break;
                    } else {
                        overlaps = true;
                        break;
                    }
                }
            }
            
            if (!overlaps) {
                filtered.add(item);
            }
        }
        
        return filtered;
    }
    
    /**
     * Checks if two rectangles overlap significantly.
     */
    private static boolean rectsOverlap(Rect rect1, Rect rect2) {
        int overlapX = Math.max(0, Math.min(rect1.x + rect1.width, rect2.x + rect2.width) - Math.max(rect1.x, rect2.x));
        int overlapY = Math.max(0, Math.min(rect1.y + rect1.height, rect2.y + rect2.height) - Math.max(rect1.y, rect2.y));
        double overlapArea = overlapX * overlapY;
        
        double area1 = rect1.width * rect1.height;
        double area2 = rect2.width * rect2.height;
        double minArea = Math.min(area1, area2);
        
        return overlapArea / minArea > 0.5; // 50% overlap threshold
    }
    
    /**
     * Clamps a rectangle to ensure it's within image bounds.
     */
    private static Rect clampRectToImage(Rect rect, Mat image) {
        int x = Math.max(0, rect.x);
        int y = Math.max(0, rect.y);
        int width = Math.min(rect.width, image.cols() - x);
        int height = Math.min(rect.height, image.rows() - y);
        
        return new Rect(x, y, width, height);
    }
}
