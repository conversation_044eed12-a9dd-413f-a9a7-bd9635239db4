package com.signaturedetection.examples;

import com.signaturedetection.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;

/**
 * Example demonstrating comprehensive extraction of both signatures and initials from TIFF files.
 */
public class SignatureAndInitialsExample {
    private static final Logger logger = LoggerFactory.getLogger(SignatureAndInitialsExample.class);
    
    public static void main(String[] args) {
        if (args.length < 1) {
            System.out.println("Usage: java SignatureAndInitialsExample <input-tiff-path>");
            System.out.println("Example: java SignatureAndInitialsExample document.tiff");
            return;
        }
        
        String inputPath = args[0];
        String outputDir = "./signatures_initials_output/";
        
        try {
            demonstrateSignatureAndInitialsExtraction(inputPath, outputDir);
        } catch (Exception e) {
            logger.error("Error in signature and initials example: {}", e.getMessage(), e);
            System.err.println("Error: " + e.getMessage());
        }
    }
    
    /**
     * Demonstrates comprehensive signature and initials extraction with different detection modes.
     */
    public static void demonstrateSignatureAndInitialsExtraction(String inputPath, String outputDir) throws IOException {
        System.out.println("=== Signature and Initials Extraction Demonstration ===");
        System.out.println("Input: " + inputPath);
        System.out.println("Output: " + outputDir);
        System.out.println();
        
        // Test 1: Default detection
        System.out.println("=== Test 1: Default Detection ===");
        testDefaultDetection(inputPath, outputDir + "/01_default/");
        
        // Test 2: Sensitive detection (finds more items)
        System.out.println("=== Test 2: Sensitive Detection ===");
        testSensitiveDetection(inputPath, outputDir + "/02_sensitive/");
        
        // Test 3: Strict detection (higher confidence)
        System.out.println("=== Test 3: Strict Detection ===");
        testStrictDetection(inputPath, outputDir + "/03_strict/");
        
        // Test 4: Custom configuration
        System.out.println("=== Test 4: Custom Configuration ===");
        testCustomConfiguration(inputPath, outputDir + "/04_custom/");
        
        // Test 5: Comparison of different approaches
        System.out.println("=== Test 5: Comparison Analysis ===");
        performComparisonAnalysis(inputPath, outputDir);
        
        System.out.println("=== Demonstration Complete ===");
        System.out.println("Check subdirectories in " + outputDir + " for results");
    }
    
    /**
     * Test with default detection parameters.
     */
    private static void testDefaultDetection(String inputPath, String outputDir) throws IOException {
        SignatureAndInitialsExtractor.ExtractionResult result = 
            SignatureAndInitialsExtractor.extractSignaturesAndInitials(inputPath, outputDir);
        
        if (result.isSuccess()) {
            System.out.println("  Default detection results:");
            System.out.println("    Signatures: " + result.getSignatureCount());
            System.out.println("    Initials: " + result.getInitialsCount());
            System.out.println("    Processing time: " + result.getProcessingTimeMs() + "ms");
            
            // Print details of detected items
            printDetectionDetails(result);
        } else {
            System.out.println("  Default detection failed: " + result.getErrorMessage());
        }
        System.out.println();
    }
    
    /**
     * Test with sensitive detection (lower thresholds).
     */
    private static void testSensitiveDetection(String inputPath, String outputDir) throws IOException {
        SignatureAndInitialsExtractor.ExtractionResult result = 
            SignatureAndInitialsExtractor.extractWithSensitiveDetection(inputPath, outputDir);
        
        if (result.isSuccess()) {
            System.out.println("  Sensitive detection results:");
            System.out.println("    Signatures: " + result.getSignatureCount());
            System.out.println("    Initials: " + result.getInitialsCount());
            System.out.println("    Processing time: " + result.getProcessingTimeMs() + "ms");
            System.out.println("    Note: May include more false positives");
        } else {
            System.out.println("  Sensitive detection failed: " + result.getErrorMessage());
        }
        System.out.println();
    }
    
    /**
     * Test with strict detection (higher thresholds).
     */
    private static void testStrictDetection(String inputPath, String outputDir) throws IOException {
        SignatureAndInitialsExtractor.ExtractionResult result = 
            SignatureAndInitialsExtractor.extractWithStrictDetection(inputPath, outputDir);
        
        if (result.isSuccess()) {
            System.out.println("  Strict detection results:");
            System.out.println("    Signatures: " + result.getSignatureCount());
            System.out.println("    Initials: " + result.getInitialsCount());
            System.out.println("    Processing time: " + result.getProcessingTimeMs() + "ms");
            System.out.println("    Note: Higher confidence, may miss some items");
        } else {
            System.out.println("  Strict detection failed: " + result.getErrorMessage());
        }
        System.out.println();
    }
    
    /**
     * Test with custom configuration optimized for specific document types.
     */
    private static void testCustomConfiguration(String inputPath, String outputDir) throws IOException {
        // Create custom configuration for documents with small initials
        SignatureAndInitialsDetector.DetectionConfig customConfig = new SignatureAndInitialsDetector.DetectionConfig();
        
        // Adjust for smaller initials
        customConfig.minInitialsArea = 100;
        customConfig.maxInitialsArea = 2000;
        customConfig.minInitialsWidth = 10;
        customConfig.minInitialsHeight = 10;
        
        // Adjust for larger signatures
        customConfig.minSignatureArea = 800;
        customConfig.minSignatureWidth = 60;
        customConfig.minSignatureHeight = 20;
        
        // Lower confidence threshold to catch more items
        customConfig.confidenceThreshold = 0.4;
        customConfig.minContourPoints = 8;
        
        SignatureAndInitialsExtractor.ExtractionResult result = 
            SignatureAndInitialsExtractor.extractSignaturesAndInitials(inputPath, outputDir, customConfig);
        
        if (result.isSuccess()) {
            System.out.println("  Custom configuration results:");
            System.out.println("    Signatures: " + result.getSignatureCount());
            System.out.println("    Initials: " + result.getInitialsCount());
            System.out.println("    Processing time: " + result.getProcessingTimeMs() + "ms");
            System.out.println("    Configuration: Optimized for small initials and large signatures");
        } else {
            System.out.println("  Custom configuration failed: " + result.getErrorMessage());
        }
        System.out.println();
    }
    
    /**
     * Performs comparison analysis of different detection approaches.
     */
    private static void performComparisonAnalysis(String inputPath, String outputDir) throws IOException {
        System.out.println("  Running comparison analysis...");
        
        try {
            // Run all detection modes
            SignatureAndInitialsExtractor.ExtractionResult defaultResult = 
                SignatureAndInitialsExtractor.extractSignaturesAndInitials(inputPath, outputDir + "/comparison_default/");
            
            SignatureAndInitialsExtractor.ExtractionResult sensitiveResult = 
                SignatureAndInitialsExtractor.extractWithSensitiveDetection(inputPath, outputDir + "/comparison_sensitive/");
            
            SignatureAndInitialsExtractor.ExtractionResult strictResult = 
                SignatureAndInitialsExtractor.extractWithStrictDetection(inputPath, outputDir + "/comparison_strict/");
            
            // Generate comparison report
            generateComparisonReport(defaultResult, sensitiveResult, strictResult, outputDir);
            
            System.out.println("  Comparison analysis completed");
            System.out.println("  Check comparison_report.txt for detailed analysis");
            
        } catch (Exception e) {
            System.out.println("  Comparison analysis failed: " + e.getMessage());
        }
        System.out.println();
    }
    
    /**
     * Prints detailed information about detected items.
     */
    private static void printDetectionDetails(SignatureAndInitialsExtractor.ExtractionResult result) {
        if (result.getSignatureCount() > 0) {
            System.out.println("    Signature details:");
            for (SignatureAndInitialsDetector.DetectedItem signature : result.getSignatures()) {
                System.out.println(String.format("      - Area: %.0f, Ratio: %.2f, Confidence: %.2f, Bounds: %s",
                        signature.getArea(), signature.getAspectRatio(), signature.getConfidence(), signature.getBoundingRect()));
            }
        }
        
        if (result.getInitialsCount() > 0) {
            System.out.println("    Initials details:");
            for (SignatureAndInitialsDetector.DetectedItem initials : result.getInitials()) {
                System.out.println(String.format("      - Area: %.0f, Ratio: %.2f, Confidence: %.2f, Bounds: %s",
                        initials.getArea(), initials.getAspectRatio(), initials.getConfidence(), initials.getBoundingRect()));
            }
        }
    }
    
    /**
     * Generates a comprehensive comparison report.
     */
    private static void generateComparisonReport(SignatureAndInitialsExtractor.ExtractionResult defaultResult,
                                               SignatureAndInitialsExtractor.ExtractionResult sensitiveResult,
                                               SignatureAndInitialsExtractor.ExtractionResult strictResult,
                                               String outputDir) throws IOException {
        
        String reportPath = outputDir + "/comparison_report.txt";
        
        try (java.io.PrintWriter writer = new java.io.PrintWriter(reportPath)) {
            writer.println("Signature and Initials Detection Comparison Report");
            writer.println("================================================");
            writer.println("Generated: " + java.time.LocalDateTime.now());
            writer.println();
            
            writer.println("Detection Mode Comparison:");
            writer.println("-------------------------");
            
            if (defaultResult.isSuccess()) {
                writer.println("Default Detection:");
                writer.println("  Signatures: " + defaultResult.getSignatureCount());
                writer.println("  Initials: " + defaultResult.getInitialsCount());
                writer.println("  Total: " + defaultResult.getTotalCount());
                writer.println("  Processing time: " + defaultResult.getProcessingTimeMs() + "ms");
                writer.println();
            }
            
            if (sensitiveResult.isSuccess()) {
                writer.println("Sensitive Detection:");
                writer.println("  Signatures: " + sensitiveResult.getSignatureCount());
                writer.println("  Initials: " + sensitiveResult.getInitialsCount());
                writer.println("  Total: " + sensitiveResult.getTotalCount());
                writer.println("  Processing time: " + sensitiveResult.getProcessingTimeMs() + "ms");
                writer.println("  Note: Lower thresholds, may include false positives");
                writer.println();
            }
            
            if (strictResult.isSuccess()) {
                writer.println("Strict Detection:");
                writer.println("  Signatures: " + strictResult.getSignatureCount());
                writer.println("  Initials: " + strictResult.getInitialsCount());
                writer.println("  Total: " + strictResult.getTotalCount());
                writer.println("  Processing time: " + strictResult.getProcessingTimeMs() + "ms");
                writer.println("  Note: Higher thresholds, higher confidence");
                writer.println();
            }
            
            writer.println("Recommendations:");
            writer.println("---------------");
            writer.println("- Use Default Detection for most documents");
            writer.println("- Use Sensitive Detection when you need to find all possible signatures/initials");
            writer.println("- Use Strict Detection when you need high confidence and can tolerate missing some items");
            writer.println("- Use Custom Configuration when you know specific characteristics of your documents");
            writer.println();
            
            writer.println("Output Directories:");
            writer.println("------------------");
            writer.println("01_default/ - Default detection results");
            writer.println("02_sensitive/ - Sensitive detection results");
            writer.println("03_strict/ - Strict detection results");
            writer.println("04_custom/ - Custom configuration results");
            writer.println("comparison_*/ - Individual comparison results");
            writer.println();
            
            writer.println("Each directory contains:");
            writer.println("- 01_preprocessed.png - Preprocessed image");
            writer.println("- 02_lines_removed.png - Image with lines removed");
            writer.println("- 03_detection_result.png - Visualization with detected items");
            writer.println("- signature_*.png - Individual signature images");
            writer.println("- initials_*.png - Individual initials images");
            writer.println("- extraction_report.txt - Detailed extraction report");
        }
        
        System.out.println("  Comparison report saved to: " + reportPath);
    }
    
    /**
     * Demonstrates how to use the extraction results programmatically.
     */
    public static void demonstrateProgrammaticUsage(String inputPath) throws IOException {
        System.out.println("=== Programmatic Usage Example ===");
        
        // Extract with default settings
        SignatureAndInitialsExtractor.ExtractionResult result = 
            SignatureAndInitialsExtractor.extractSignaturesAndInitials(inputPath, "./temp_output/");
        
        if (result.isSuccess()) {
            // Access signatures
            for (SignatureAndInitialsDetector.DetectedItem signature : result.getSignatures()) {
                System.out.println("Found signature at: " + signature.getBoundingRect());
                System.out.println("  Confidence: " + signature.getConfidence());
                System.out.println("  Area: " + signature.getArea());
            }
            
            // Access initials
            for (SignatureAndInitialsDetector.DetectedItem initials : result.getInitials()) {
                System.out.println("Found initials at: " + initials.getBoundingRect());
                System.out.println("  Confidence: " + initials.getConfidence());
                System.out.println("  Area: " + initials.getArea());
            }
            
            // Access extracted images
            for (SignatureAndInitialsDetector.ExtractedItem item : result.getExtractedItems()) {
                System.out.println("Extracted " + item.getType() + " " + item.getId());
                // item.getImage() contains the actual image data
            }
            
            // Generate summary
            System.out.println(result.generateSummary());
            
        } else {
            System.out.println("Extraction failed: " + result.getErrorMessage());
        }
    }
}
