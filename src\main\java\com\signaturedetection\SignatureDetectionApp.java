package com.signaturedetection;

import org.opencv.core.Mat;
import org.opencv.core.Rect;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.List;
import java.util.ArrayList;

/**
 * Main application class for signature detection and line removal from TIFF images.
 * Provides a complete pipeline for processing scanned documents.
 */
public class SignatureDetectionApp {
    private static final Logger logger = LoggerFactory.getLogger(SignatureDetectionApp.class);
    
    public static void main(String[] args) {

        /* 
        if (args.length < 1) {
            System.out.println("Usage: java -jar signature-detection.jar <input-tiff-path> [output-directory]");
            System.out.println("Example: java -jar signature-detection.jar document.tiff ./output/");
            return;
        }*/
        
        String inputPath = "c:\\InfoInputSolution\\batches\\b4b9fab0-b25b-11ef-80a5-64d69a1832f2\\26274\\14.tif";
        String outputDir = "./output/";
        
        try {
            // Use the enhanced signature and initials extractor
            SignatureAndInitialsExtractor.ExtractionResult result =
                SignatureAndInitialsExtractor.extractSignaturesAndInitials(inputPath, outputDir);

            // Print results for testing
            System.out.println("=== SIGNATURE AND INITIALS EXTRACTION RESULTS ===");
            System.out.println(result.generateSummary());
            System.out.println();

            if (result.isSuccess()) {
                System.out.println("Extraction completed successfully!");
                System.out.println("Found " + result.getSignatureCount() + " signatures and " +
                                 result.getInitialsCount() + " initials");
                System.out.println("Processing time: " + result.getProcessingTimeMs() + "ms");
                System.out.println("Check output directory: " + outputDir);
            } else {
                System.err.println("Extraction failed: " + result.getErrorMessage());
            }

        } catch (Exception e) {
            logger.error("Error processing document: {}", e.getMessage(), e);
            System.err.println("Error: " + e.getMessage());
        }
    }
    
    /**
     * Complete processing pipeline for signature detection and line removal.
     * Returns a structured result object with coordinates and statistics.
     *
     * @param inputPath Path to the input TIFF image
     * @param outputDir Directory to save output files
     * @return SignatureDetectionResult containing all detection results and coordinates
     * @throws IOException if file operations fail
     */
    public static SignatureDetectionResult processDocumentWithResult(String inputPath, String outputDir) throws IOException {
        long startTime = System.currentTimeMillis();

        try {
            logger.info("Starting document processing: {}", inputPath);

            // Create output directory if it doesn't exist
            java.io.File outputDirectory = new java.io.File(outputDir);
            if (!outputDirectory.exists()) {
                outputDirectory.mkdirs();
            } else 
            {
                // Clear the directory
                for (java.io.File file : outputDirectory.listFiles()) {
                    file.delete();
                }
            }
            // Step 1: Load the TIFF image
            Mat originalImage = ImageProcessor.loadTiffImage(inputPath);

            // Step 2: Preprocess the image with noise removal
            Mat preprocessedImage = ImageProcessor.preprocessImage(originalImage);

            // Step 3: Remove lines from the image
            Mat imageWithoutLines = LineRemover.removeLines(preprocessedImage);

            // Step 4: Detect signature regions and count contours
            List<Rect> signatureRegions = SignatureDetector.detectSignatureRegions(imageWithoutLines);

            // Count total contours for statistics
            int totalContours = countContours(imageWithoutLines);

            // Step 5: Extract signatures
            List<Mat> extractedSignatures = SignatureDetector.extractSignatures(originalImage, signatureRegions);

            // Step 6: Save results
            saveResults(originalImage, imageWithoutLines, extractedSignatures, signatureRegions, outputDir);

            // Create processing statistics
            long processingTime = System.currentTimeMillis() - startTime;
            SignatureDetectionResult.ProcessingStats stats = new SignatureDetectionResult.ProcessingStats(
                processingTime,
                originalImage.cols(),
                originalImage.rows(),
                totalContours,
                true, // lines were removed
                inputPath
            );

            // Clean up
            originalImage.release();
            preprocessedImage.release();
            imageWithoutLines.release();
            for (Mat signature : extractedSignatures) {
                signature.release();
            }

            logger.info("Document processing completed successfully");
            return new SignatureDetectionResult(signatureRegions, stats);

        } catch (Exception e) {
            logger.error("Error processing document: {}", e.getMessage(), e);
            return new SignatureDetectionResult("Processing failed: " + e.getMessage());
        }
    }

    /**
     * Complete processing pipeline for signature detection and line removal.
     *
     * @param inputPath Path to the input TIFF image
     * @param outputDir Directory to save output files
     * @throws IOException if file operations fail
     */
    public static void processDocument(String inputPath, String outputDir) throws IOException {
        logger.info("Starting document processing: {}", inputPath);
        
        // Create output directory if it doesn't exist
        java.io.File outputDirectory = new java.io.File(outputDir);
        if (!outputDirectory.exists()) {
            outputDirectory.mkdirs();
        }
        
        // Step 1: Load the TIFF image
        Mat originalImage = ImageProcessor.loadTiffImage(inputPath);
        
        // Step 2: Preprocess the image
        Mat preprocessedImage = ImageProcessor.preprocessImage(originalImage);
        
        // Step 3: Remove lines from the image
        Mat imageWithoutLines = LineRemover.removeLines(preprocessedImage);
        
        // Step 4: Detect signature regions
        List<Rect> signatureRegions = SignatureDetector.detectSignatureRegions(imageWithoutLines);
        
        // Step 5: Extract signatures
        List<Mat> extractedSignatures = SignatureDetector.extractSignatures(originalImage, signatureRegions);
        
        // Step 6: Save results
        saveResults(originalImage, imageWithoutLines, extractedSignatures, signatureRegions, outputDir);
        
        // Clean up
        originalImage.release();
        preprocessedImage.release();
        imageWithoutLines.release();
        for (Mat signature : extractedSignatures) {
            signature.release();
        }
        
        logger.info("Document processing completed successfully");
    }
    
    /**
     * Saves all processing results to the output directory.
     */
    private static void saveResults(Mat originalImage, Mat processedImage, 
                                  List<Mat> signatures, List<Rect> signatureRegions, 
                                  String outputDir) throws IOException {
        
        // Save processed image (with lines removed)
        String processedPath = outputDir + "/processed_image.png";
        ImageProcessor.saveImage(processedImage, processedPath);
        logger.info("Saved processed image: {}", processedPath);
        
        // Save visualization with signature regions highlighted
        Mat visualization = ImageProcessor.visualizeSignatureRegions(originalImage, signatureRegions);
        String visualizationPath = outputDir + "/signature_detection_result.png";
        ImageProcessor.saveImage(visualization, visualizationPath);
        visualization.release();
        logger.info("Saved visualization: {}", visualizationPath);
        
        // Save individual signatures
        for (int i = 0; i < signatures.size(); i++) {
            String signaturePath = outputDir + "/signature_" + (i + 1) + ".png";
            ImageProcessor.saveImage(signatures.get(i), signaturePath);
            logger.info("Saved signature {}: {}", i + 1, signaturePath);
        }
        
        // Save summary report
        saveSummaryReport(signatureRegions, outputDir);
    }
    
    /**
     * Saves a text summary of the detection results.
     */
    private static void saveSummaryReport(List<Rect> signatureRegions, String outputDir) throws IOException {
        String reportPath = outputDir + "/detection_report.txt";
        
        try (java.io.PrintWriter writer = new java.io.PrintWriter(reportPath)) {
            writer.println("Signature Detection Report");
            writer.println("=========================");
            writer.println("Generated: " + java.time.LocalDateTime.now());
            writer.println();
            writer.println("Total signatures detected: " + signatureRegions.size());
            writer.println();
            
            for (int i = 0; i < signatureRegions.size(); i++) {
                Rect region = signatureRegions.get(i);
                writer.println("Signature " + (i + 1) + ":");
                writer.println("  Position: (" + region.x + ", " + region.y + ")");
                writer.println("  Size: " + region.width + "x" + region.height);
                writer.println("  Area: " + (region.width * region.height) + " pixels");
                writer.println();
            }
        }
        
        logger.info("Saved detection report: {}", reportPath);
    }
    
    /**
     * Processes a document with custom parameters for fine-tuning.
     * 
     * @param inputPath Path to input TIFF
     * @param outputDir Output directory
     * @param horizontalKernelSize Custom horizontal line removal kernel size
     * @param verticalKernelSize Custom vertical line removal kernel size
     * @param minSignatureArea Minimum signature area
     * @param maxSignatureArea Maximum signature area
     * @param minAspectRatio Minimum signature aspect ratio
     * @param maxAspectRatio Maximum signature aspect ratio
     */
    public static void processDocumentCustom(String inputPath, String outputDir,
                                           int horizontalKernelSize, int verticalKernelSize,
                                           int minSignatureArea, int maxSignatureArea,
                                           double minAspectRatio, double maxAspectRatio) throws IOException {
        
        logger.info("Processing document with custom parameters");
        
        // Create output directory
        java.io.File outputDirectory = new java.io.File(outputDir);
        if (!outputDirectory.exists()) {
            outputDirectory.mkdirs();
        }
        
        // Load and preprocess
        Mat originalImage = ImageProcessor.loadTiffImage(inputPath);
        Mat preprocessedImage = ImageProcessor.preprocessImage(originalImage);
        
        // Remove lines with custom parameters
        Mat imageWithoutLines = LineRemover.removeLinesCustom(preprocessedImage, 
                                                            horizontalKernelSize, verticalKernelSize);
        
        // Detect signatures with custom parameters
        List<Rect> signatureRegions = SignatureDetector.detectSignatureRegionsCustom(
            imageWithoutLines, minSignatureArea, maxSignatureArea, minAspectRatio, maxAspectRatio);
        
        // Extract and save results
        List<Mat> extractedSignatures = SignatureDetector.extractSignatures(originalImage, signatureRegions);
        saveResults(originalImage, imageWithoutLines, extractedSignatures, signatureRegions, outputDir);
        
        // Clean up
        originalImage.release();
        preprocessedImage.release();
        imageWithoutLines.release();
        for (Mat signature : extractedSignatures) {
            signature.release();
        }
    }

    /**
     * Counts the total number of contours in an image for statistics.
     */
    private static int countContours(Mat image) {
        List<org.opencv.core.MatOfPoint> contours = new ArrayList<>();
        Mat hierarchy = new Mat();
        org.opencv.imgproc.Imgproc.findContours(image, contours, hierarchy,
                                               org.opencv.imgproc.Imgproc.RETR_EXTERNAL,
                                               org.opencv.imgproc.Imgproc.CHAIN_APPROX_SIMPLE);
        int count = contours.size();
        hierarchy.release();
        return count;
    }

    /**
     * Saves the detection results to a JSON file for easy access and testing.
     */
    private static void saveResultsToFile(SignatureDetectionResult result, String outputDir) throws IOException {
        String resultsPath = outputDir + "/detection_results.json";

        try (java.io.PrintWriter writer = new java.io.PrintWriter(resultsPath)) {
            writer.println(result.toJsonString());
        }

        logger.info("Saved detection results: {}", resultsPath);
        System.out.println("Results saved to: " + resultsPath);
    }

    /**
     * Process document with advanced noise removal for heavily degraded images.
     *
     * @param inputPath Path to the input TIFF image
     * @param outputDir Directory to save output files
     * @return SignatureDetectionResult containing all detection results
     * @throws IOException if file operations fail
     */
    public static SignatureDetectionResult processDocumentWithAdvancedDenoising(String inputPath, String outputDir) throws IOException {
        long startTime = System.currentTimeMillis();

        try {
            logger.info("Starting document processing with advanced denoising: {}", inputPath);

            // Create output directory
            java.io.File outputDirectory = new java.io.File(outputDir);
            if (!outputDirectory.exists()) {
                outputDirectory.mkdirs();
            } else {
                // Clear the directory
                for (java.io.File file : outputDirectory.listFiles()) {
                    file.delete();
                }
            }

            // Step 1: Load the TIFF image
            Mat originalImage = ImageProcessor.loadTiffImage(inputPath);

            // Step 2: Advanced preprocessing with comprehensive noise removal
            Mat preprocessedImage = ImageProcessor.preprocessImageAdvanced(originalImage);

            // Save denoised image for inspection
            String denoisedPath = outputDir + "/01_denoised.png";
            ImageProcessor.saveImage(preprocessedImage, denoisedPath);
            logger.info("Saved denoised image: {}", denoisedPath);

            // Step 3: Remove lines from the denoised image
            Mat imageWithoutLines = LineRemover.removeLines(preprocessedImage);

            // Step 4: Detect signature regions
            List<Rect> signatureRegions = SignatureDetector.detectSignatureRegions(imageWithoutLines);

            // Count total contours for statistics
            int totalContours = countContours(imageWithoutLines);

            // Step 5: Extract signatures
            List<Mat> extractedSignatures = SignatureDetector.extractSignatures(originalImage, signatureRegions);

            // Step 6: Save results
            saveResults(originalImage, imageWithoutLines, extractedSignatures, signatureRegions, outputDir);

            // Create processing statistics
            long processingTime = System.currentTimeMillis() - startTime;
            SignatureDetectionResult.ProcessingStats stats = new SignatureDetectionResult.ProcessingStats(
                processingTime,
                originalImage.cols(),
                originalImage.rows(),
                totalContours,
                true, // lines were removed
                inputPath
            );

            // Clean up
            originalImage.release();
            preprocessedImage.release();
            imageWithoutLines.release();
            for (Mat signature : extractedSignatures) {
                signature.release();
            }

            logger.info("Document processing with advanced denoising completed successfully");
            return new SignatureDetectionResult(signatureRegions, stats);

        } catch (Exception e) {
            logger.error("Error processing document with advanced denoising: {}", e.getMessage(), e);
            return new SignatureDetectionResult("Advanced processing failed: " + e.getMessage());
        }
    }

    /**
     * Process document with custom noise removal configuration.
     *
     * @param inputPath Path to the input TIFF image
     * @param outputDir Directory to save output files
     * @param noiseConfig Custom noise removal configuration
     * @return SignatureDetectionResult containing all detection results
     * @throws IOException if file operations fail
     */
    public static SignatureDetectionResult processDocumentWithCustomDenoising(String inputPath, String outputDir,
                                                                             NoiseRemover.NoiseRemovalConfig noiseConfig) throws IOException {
        long startTime = System.currentTimeMillis();

        try {
            logger.info("Starting document processing with custom denoising: {}", inputPath);

            // Create output directory
            java.io.File outputDirectory = new java.io.File(outputDir);
            if (!outputDirectory.exists()) {
                outputDirectory.mkdirs();
            } else {
                // Clear the directory
                for (java.io.File file : outputDirectory.listFiles()) {
                    file.delete();
                }
            }

            // Step 1: Load the TIFF image
            Mat originalImage = ImageProcessor.loadTiffImage(inputPath);

            // Step 2: Preprocess with custom noise removal
            Mat preprocessedImage = ImageProcessor.preprocessImage(originalImage, noiseConfig);

            // Step 3: Remove lines
            Mat imageWithoutLines = LineRemover.removeLines(preprocessedImage);

            // Step 4: Detect signatures
            List<Rect> signatureRegions = SignatureDetector.detectSignatureRegions(imageWithoutLines);
            int totalContours = countContours(imageWithoutLines);

            // Step 5: Extract signatures
            List<Mat> extractedSignatures = SignatureDetector.extractSignatures(originalImage, signatureRegions);

            // Step 6: Save results
            saveResults(originalImage, imageWithoutLines, extractedSignatures, signatureRegions, outputDir);

            // Create processing statistics
            long processingTime = System.currentTimeMillis() - startTime;
            SignatureDetectionResult.ProcessingStats stats = new SignatureDetectionResult.ProcessingStats(
                processingTime,
                originalImage.cols(),
                originalImage.rows(),
                totalContours,
                true,
                inputPath
            );

            // Clean up
            originalImage.release();
            preprocessedImage.release();
            imageWithoutLines.release();
            for (Mat signature : extractedSignatures) {
                signature.release();
            }

            logger.info("Document processing with custom denoising completed successfully");
            return new SignatureDetectionResult(signatureRegions, stats);

        } catch (Exception e) {
            logger.error("Error processing document with custom denoising: {}", e.getMessage(), e);
            return new SignatureDetectionResult("Custom denoising failed: " + e.getMessage());
        }
    }
}
